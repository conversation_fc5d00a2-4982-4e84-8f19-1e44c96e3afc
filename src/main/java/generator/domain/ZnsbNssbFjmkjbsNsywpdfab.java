package generator.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 非居民跨境办税纳税义务判断方案表
 * @TableName znsb_nssb_fjmkjbs_nsywpdfab
 */
@TableName(value ="znsb_nssb_fjmkjbs_nsywpdfab")
@Data
public class ZnsbNssbFjmkjbsNsywpdfab {
    /**
     * UUID||uuid
     */
    @TableId(value = "uuid")
    private String uuid;

    /**
     * 登记序号
     */
    @TableField(value = "djxh")
    private Long djxh;

    /**
     * 扣缴申报编码
     */
    @TableField(value = "kjsbbm")
    private String kjsbbm;

    /**
     * 问题选项
     */
    @TableField(value = "wtxx")
    private String wtxx;

    /**
     * 方案名称
     */
    @TableField(value = "famc")
    private String famc;

    /**
     * 作废标志
     */
    @TableField(value = "zfbz_1")
    private String zfbz_1;

    /**
     * 作废人代码
     */
    @TableField(value = "zfr_dm")
    private String zfr_dm;

    /**
     * 作废日期
     */
    @TableField(value = "zfrq_1")
    private Date zfrq_1;

    /**
     * 适用范围||用于参数表中区别纳税人端和局端配置
     */
    @TableField(value = "syfw")
    private String syfw;

    /**
     * 业务渠道代码
     */
    @TableField(value = "ywqd_dm")
    private String ywqd_dm;

    /**
     * 录入日期
     */
    @TableField(value = "lrrq")
    private Date lrrq;

    /**
     * 修改日期
     */
    @TableField(value = "xgrq")
    private Date xgrq;

    /**
     * 数据产生地区
     */
    @TableField(value = "sjcsdq")
    private String sjcsdq;

    /**
     * 数据归属地区
     */
    @TableField(value = "sjgsdq")
    private String sjgsdq;

    /**
     * 修改人身份id
     */
    @TableField(value = "xgrsfid")
    private String xgrsfid;

    /**
     * 录入人身份id
     */
    @TableField(value = "lrrsfid")
    private String lrrsfid;

    /**
     * 数据同步时间
     */
    @TableField(value = "sjtb_sj")
    private Date sjtb_sj;

    /**
     * 支付费用类型编码
     */
    @TableField(value = "zffylxbm")
    private String zffylxbm;

    /**
     * 上级支付费用类型编码
     */
    @TableField(value = "sjzffylxbm")
    private String sjzffylxbm;

    /**
     * 企业所得税税率
     */
    @TableField(value = "qysdssl")
    private BigDecimal qysdssl;

    /**
     * 增值税税率||增值税税率
     */
    @TableField(value = "zzssl")
    private BigDecimal zzssl;

    /**
     * 文化事业建设费税率
     */
    @TableField(value = "whsyjsfsl")
    private BigDecimal whsyjsfsl;

    /**
     * 印花税税率
     */
    @TableField(value = "yhssl")
    private BigDecimal yhssl;

    /**
     * 房产税税率
     */
    @TableField(value = "fcssl")
    private BigDecimal fcssl;
}
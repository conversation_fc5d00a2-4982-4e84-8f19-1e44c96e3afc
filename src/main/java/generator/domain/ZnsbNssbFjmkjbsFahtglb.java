package generator.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Data;

/**
 * 非居民跨境办税方案合同关联表
 * @TableName znsb_nssb_fjmkjbs_fahtglb
 */
@TableName(value ="znsb_nssb_fjmkjbs_fahtglb")
@Data
public class ZnsbNssbFjmkjbsFahtglb {
    /**
     * UUID||uuid
     */
    @TableId(value = "uuid")
    private String uuid;

    /**
     * 登记序号
     */
    @TableField(value = "djxh")
    private Long djxh;

    /**
     * 扣缴申报编码
     */
    @TableField(value = "kjsbbm")
    private String kjsbbm;

    /**
     * 非居民跨境办税纳税义务判断方案表UUID
     */
    @TableField(value = "fjmkjbsnsywpdfabuuid")
    private String fjmkjbsnsywpdfabuuid;

    /**
     * 系统合同编号
     */
    @TableField(value = "xthtbh")
    private String xthtbh;

    /**
     * 方案合同绑定日期
     */
    @TableField(value = "fahtbdrq")
    private Date fahtbdrq;

    /**
     * 方案合同解绑日期
     */
    @TableField(value = "fahtjbrq")
    private Date fahtjbrq;

    /**
     * 有效标志
     */
    @TableField(value = "yxbz")
    private String yxbz;

    /**
     * 业务渠道代码
     */
    @TableField(value = "ywqd_dm")
    private String ywqd_dm;

    /**
     * 录入日期
     */
    @TableField(value = "lrrq")
    private Date lrrq;

    /**
     * 修改日期
     */
    @TableField(value = "xgrq")
    private Date xgrq;

    /**
     * 数据产生地区
     */
    @TableField(value = "sjcsdq")
    private String sjcsdq;

    /**
     * 数据归属地区
     */
    @TableField(value = "sjgsdq")
    private String sjgsdq;

    /**
     * 修改人身份id
     */
    @TableField(value = "xgrsfid")
    private String xgrsfid;

    /**
     * 录入人身份id
     */
    @TableField(value = "lrrsfid")
    private String lrrsfid;

    /**
     * 数据同步时间
     */
    @TableField(value = "sjtb_sj")
    private Date sjtb_sj;
}
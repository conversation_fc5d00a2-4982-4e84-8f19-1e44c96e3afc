package generator.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import generator.domain.ZnsbNssbFjmkjbsNsywpdfab;
import generator.service.ZnsbNssbFjmkjbsNsywpdfabService;
import generator.mapper.ZnsbNssbFjmkjbsNsywpdfabMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【znsb_nssb_fjmkjbs_nsywpdfab(非居民跨境办税纳税义务判断方案表)】的数据库操作Service实现
* @createDate 2025-09-29 16:54:40
*/
@Service
public class ZnsbNssbFjmkjbsNsywpdfabServiceImpl extends ServiceImpl<ZnsbNssbFjmkjbsNsywpdfabMapper, ZnsbNssbFjmkjbsNsywpdfab>
    implements ZnsbNssbFjmkjbsNsywpdfabService{

}





<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="generator.mapper.ZnsbNssbFjmkjbsNsywpdfabMapper">

    <resultMap id="BaseResultMap" type="generator.domain.ZnsbNssbFjmkjbsNsywpdfab">
            <id property="uuid" column="uuid" jdbcType="VARCHAR"/>
            <result property="djxh" column="djxh" jdbcType="DECIMAL"/>
            <result property="kjsbbm" column="kjsbbm" jdbcType="CHAR"/>
            <result property="wtxx" column="wtxx" jdbcType="VARCHAR"/>
            <result property="famc" column="famc" jdbcType="VARCHAR"/>
            <result property="zfbz_1" column="zfbz_1" jdbcType="CHAR"/>
            <result property="zfr_dm" column="zfr_dm" jdbcType="CHAR"/>
            <result property="zfrq_1" column="zfrq_1" jdbcType="TIMESTAMP"/>
            <result property="syfw" column="syfw" jdbcType="VARCHAR"/>
            <result property="ywqd_dm" column="ywqd_dm" jdbcType="VARCHAR"/>
            <result property="lrrq" column="lrrq" jdbcType="TIMESTAMP"/>
            <result property="xgrq" column="xgrq" jdbcType="TIMESTAMP"/>
            <result property="sjcsdq" column="sjcsdq" jdbcType="CHAR"/>
            <result property="sjgsdq" column="sjgsdq" jdbcType="CHAR"/>
            <result property="xgrsfid" column="xgrsfid" jdbcType="VARCHAR"/>
            <result property="lrrsfid" column="lrrsfid" jdbcType="VARCHAR"/>
            <result property="sjtb_sj" column="sjtb_sj" jdbcType="TIMESTAMP"/>
            <result property="zffylxbm" column="zffylxbm" jdbcType="CHAR"/>
            <result property="sjzffylxbm" column="sjzffylxbm" jdbcType="CHAR"/>
            <result property="qysdssl" column="qysdssl" jdbcType="DECIMAL"/>
            <result property="zzssl" column="zzssl" jdbcType="DECIMAL"/>
            <result property="whsyjsfsl" column="whsyjsfsl" jdbcType="DECIMAL"/>
            <result property="yhssl" column="yhssl" jdbcType="DECIMAL"/>
            <result property="fcssl" column="fcssl" jdbcType="DECIMAL"/>
    </resultMap>

    <sql id="Base_Column_List">
        uuid,djxh,kjsbbm,
        wtxx,famc,zfbz_1,
        zfr_dm,zfrq_1,syfw,
        ywqd_dm,lrrq,xgrq,
        sjcsdq,sjgsdq,xgrsfid,
        lrrsfid,sjtb_sj,zffylxbm,
        sjzffylxbm,qysdssl,zzssl,
        whsyjsfsl,yhssl,fcssl
    </sql>
</mapper>
